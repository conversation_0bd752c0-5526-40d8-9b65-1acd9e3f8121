'use client';

const Services = () => {
  const services = [
    {
      title: "3D Modeling & Animation",
      description: "Create stunning 3D models and animations for games, films, architecture, and product visualization. From concept to final render.",
      features: ["Character Modeling", "Environment Design", "Product Visualization", "Animation Rigging"],
      icon: "🎭",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      title: "Web Development",
      description: "Build modern, responsive web applications using the latest technologies. Fast, scalable, and user-friendly solutions.",
      features: ["React & Next.js", "Three.js Integration", "Responsive Design", "Performance Optimization"],
      icon: "🚀",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      title: "Interactive Design",
      description: "Design engaging user interfaces with smooth animations and interactive elements that captivate and convert users.",
      features: ["UI/UX Design", "Interactive Prototypes", "Animation Design", "User Research"],
      icon: "✨",
      gradient: "from-green-500 to-teal-500"
    },
    {
      title: "Consulting",
      description: "Strategic guidance on 3D implementation, web architecture, and digital transformation to help your business succeed.",
      features: ["Technical Strategy", "Project Planning", "Team Training", "Code Review"],
      icon: "🎯",
      gradient: "from-orange-500 to-red-500"
    }
  ];

  return (
    <section id="services" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            My{' '}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Services
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive digital solutions tailored to bring your ideas to life with 
            cutting-edge technology and creative expertise.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="group relative p-8 bg-muted/30 backdrop-blur-sm rounded-2xl border border-border hover:border-primary/50 transition-all duration-500 hover:transform hover:scale-105"
            >
              {/* Background gradient on hover */}
              <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-500`}></div>

              {/* Icon */}
              <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>

              {/* Content */}
              <h3 className="text-2xl font-bold text-foreground mb-4 group-hover:text-primary transition-colors duration-300">
                {service.title}
              </h3>
              
              <p className="text-muted-foreground mb-6 leading-relaxed">
                {service.description}
              </p>

              {/* Features */}
              <div className="space-y-2">
                {service.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <span className="text-sm text-muted-foreground">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Learn More Button */}
              <div className="mt-6">
                <button className="text-primary font-semibold hover:text-secondary transition-colors duration-300 flex items-center space-x-2 group-hover:translate-x-2 transition-transform duration-300">
                  <span>Learn More</span>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex flex-col items-center space-y-4">
            <p className="text-lg text-muted-foreground">
              Need a custom solution? Let's discuss your project.
            </p>
            <button className="px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105">
              Start a Project
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
