'use client';

const WhatWeOffer = () => {
  const offerings = [
    {
      title: "Custom 3D Modeling",
      description: "High-quality 3D models for games, architecture, and product visualization",
      icon: "🎨"
    },
    {
      title: "Web Development",
      description: "Modern web applications using React, Next.js, and Three.js",
      icon: "💻"
    },
    {
      title: "Interactive UI/UX Design",
      description: "Engaging user interfaces with smooth animations and interactions",
      icon: "✨"
    }
  ];

  return (
    <section className="py-20 bg-gray-800/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Transforming Ideas into{' '}
            <span className="bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent">
              Digital Reality
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            I specialize in creating immersive digital experiences that bridge the gap
            between imagination and reality through cutting-edge technology.
          </p>
        </div>

        {/* Offerings Grid */}
        <div className="grid md:grid-cols-3 gap-8">
          {offerings.map((offering, index) => (
            <div
              key={index}
              className="group relative p-8 bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700 hover:border-indigo-500/50 transition-all duration-300 hover:transform hover:scale-105"
            >
              {/* Icon */}
              <div className="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">
                {offering.icon}
              </div>

              {/* Content */}
              <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-indigo-400 transition-colors duration-300">
                {offering.title}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {offering.description}
              </p>

              {/* Hover glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-xl opacity-0
                            group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center space-x-2 text-indigo-400 font-semibold">
            <span>Ready to bring your vision to life?</span>
            <svg className="w-5 h-5 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatWeOffer;
