@import "tailwindcss";

:root {
  /* Galaxy Theme Colors */
  --background: #0a0a1a;
  --foreground: #ffffff;
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --muted: #1e1e2e;
  --border: #2a2a3a;

  /* Glow Effects */
  --glow-primary: 0 0 20px rgba(99, 102, 241, 0.5);
  --glow-secondary: 0 0 20px rgba(139, 92, 246, 0.5);
  --glow-accent: 0 0 20px rgba(6, 182, 212, 0.5);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: #0a0a1a;
  color: #ffffff;
  font-family: var(--font-inter), sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-poppins), sans-serif;
  font-weight: 700;
  line-height: 1.2;
}

/* Glow effects for interactive elements */
.glow-primary {
  box-shadow: var(--glow-primary);
}

.glow-secondary {
  box-shadow: var(--glow-secondary);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease;
}

/* Particle background container */
#particles-js {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* Layout improvements */
.section-padding {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

@media (min-width: 768px) {
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

/* Text utilities */
.text-gradient {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

/* Ensure proper spacing between sections */
section {
  position: relative;
  z-index: 1;
}

/* Fix any potential layout issues */
.container-custom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 2rem;
  }
}
