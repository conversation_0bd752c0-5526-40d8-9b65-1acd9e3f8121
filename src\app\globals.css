@import "tailwindcss";

:root {
  /* Galaxy Theme Colors */
  --background: #0a0a1a;
  --foreground: #ffffff;
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --muted: #1e1e2e;
  --border: #2a2a3a;

  /* Glow Effects */
  --glow-primary: 0 0 20px rgba(99, 102, 241, 0.5);
  --glow-secondary: 0 0 20px rgba(139, 92, 246, 0.5);
  --glow-accent: 0 0 20px rgba(6, 182, 212, 0.5);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-poppins), sans-serif;
  font-weight: 700;
  line-height: 1.2;
}

/* Glow effects for interactive elements */
.glow-primary {
  box-shadow: var(--glow-primary);
}

.glow-secondary {
  box-shadow: var(--glow-secondary);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease;
}

/* Particle background container */
#particles-js {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
