{"name": "tb2-test", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@types/three": "^0.177.0", "d3": "^7.9.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "three": "^0.177.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}