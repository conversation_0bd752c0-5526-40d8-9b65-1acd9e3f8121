'use client';

import { useState } from 'react';

const Portfolio = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const projects = [
    {
      id: 1,
      title: "3D Character Model",
      category: "3d",
      description: "High-poly character model for gaming with detailed textures and rigging.",
      image: "/api/placeholder/400/300",
      technologies: ["Blender", "Substance Painter", "ZBrush"],
      link: "#"
    },
    {
      id: 2,
      title: "E-commerce Platform",
      category: "web",
      description: "Modern e-commerce platform with 3D product visualization.",
      image: "/api/placeholder/400/300",
      technologies: ["Next.js", "Three.js", "Stripe"],
      link: "#"
    },
    {
      id: 3,
      title: "Interactive Dashboard",
      category: "interactive",
      description: "Real-time data visualization dashboard with animated charts.",
      image: "/api/placeholder/400/300",
      technologies: ["React", "D3.js", "WebGL"],
      link: "#"
    },
    {
      id: 4,
      title: "Architectural Visualization",
      category: "3d",
      description: "Photorealistic architectural rendering for real estate.",
      image: "/api/placeholder/400/300",
      technologies: ["Blender", "Cycles", "Photoshop"],
      link: "#"
    },
    {
      id: 5,
      title: "Portfolio Website",
      category: "web",
      description: "Creative portfolio website with WebGL animations.",
      image: "/api/placeholder/400/300",
      technologies: ["Next.js", "Three.js", "GSAP"],
      link: "#"
    },
    {
      id: 6,
      title: "VR Experience",
      category: "interactive",
      description: "Immersive VR experience for product showcase.",
      image: "/api/placeholder/400/300",
      technologies: ["Unity", "C#", "Oculus SDK"],
      link: "#"
    }
  ];

  const filters = [
    { id: 'all', label: 'All Projects' },
    { id: '3d', label: '3D Modeling' },
    { id: 'web', label: 'Web Development' },
    { id: 'interactive', label: 'Interactive' }
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <section id="portfolio" className="py-20 bg-muted/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            My{' '}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Portfolio
            </span>
          </h2>
          <p className="text-xl text-foreground/80 max-w-3xl mx-auto">
            A showcase of my latest projects spanning 3D modeling, web development,
            and interactive design.
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                activeFilter === filter.id
                  ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-lg'
                  : 'bg-muted/30 text-muted-foreground hover:text-primary hover:bg-muted/50'
              }`}
            >
              {filter.label}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className="group relative bg-muted/30 backdrop-blur-sm rounded-2xl overflow-hidden border border-border hover:border-primary/50 transition-all duration-500 hover:transform hover:scale-105"
            >
              {/* Project Image */}
              <div className="relative h-48 overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                  <span className="text-6xl opacity-50">🎨</span>
                </div>
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <button className="px-6 py-3 bg-white text-black font-semibold rounded-lg transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                    View Project
                  </button>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors duration-300">
                  {project.title}
                </h3>
                
                <p className="text-muted-foreground mb-4 text-sm leading-relaxed">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.map((tech, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Project Link */}
                <a
                  href={project.link}
                  className="inline-flex items-center space-x-2 text-primary font-semibold hover:text-secondary transition-colors duration-300"
                >
                  <span>View Details</span>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* View All Projects Button */}
        <div className="text-center mt-12">
          <button className="px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105">
            View All Projects
          </button>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
