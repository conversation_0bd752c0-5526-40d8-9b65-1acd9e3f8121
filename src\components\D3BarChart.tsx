'use client';

import { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';

interface BrandData {
  name: string;
  value: number;
  year: number;
  color: string;
}

const D3BarChart = () => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [currentYear, setCurrentYear] = useState(1900);
  const [isPlaying, setIsPlaying] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Sample data for demonstration
  const sampleData: BrandData[] = [
    { name: 'Apple', value: 355000, year: 2023, color: '#007AFF' },
    { name: 'Microsoft', value: 290000, year: 2023, color: '#00BCF2' },
    { name: 'Amazon', value: 254000, year: 2023, color: '#FF9900' },
    { name: 'Google', value: 191000, year: 2023, color: '#4285F4' },
    { name: 'Samsung', value: 87000, year: 2023, color: '#1428A0' },
    { name: 'Tesla', value: 66000, year: 2023, color: '#CC0000' },
    { name: 'Meta', value: 56000, year: 2023, color: '#1877F2' },
    { name: 'NVIDIA', value: 48000, year: 2023, color: '#76B900' },
    { name: 'Netflix', value: 45000, year: 2023, color: '#E50914' },
    { name: 'Adobe', value: 42000, year: 2023, color: '#FF0000' },
  ];

  const generateYearData = (year: number) => {
    return sampleData.map(brand => ({
      ...brand,
      year,
      value: brand.value * (0.5 + Math.random() * 0.8) * (year / 2023)
    })).sort((a, b) => b.value - a.value).slice(0, 10);
  };

  useEffect(() => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    const margin = { top: 20, right: 30, bottom: 40, left: 100 };
    const width = 800 - margin.left - margin.right;
    const height = 500 - margin.top - margin.bottom;

    svg.selectAll("*").remove();

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = generateYearData(currentYear);
    
    const x = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.value) || 0])
      .range([0, width]);

    const y = d3.scaleBand()
      .domain(data.map(d => d.name))
      .range([0, height])
      .padding(0.1);

    // Bars
    g.selectAll(".bar")
      .data(data)
      .enter()
      .append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", d => y(d.name) || 0)
      .attr("width", d => x(d.value))
      .attr("height", y.bandwidth())
      .attr("fill", d => d.color)
      .attr("rx", 4)
      .style("opacity", 0.8);

    // Brand names
    g.selectAll(".label")
      .data(data)
      .enter()
      .append("text")
      .attr("class", "label")
      .attr("x", -10)
      .attr("y", d => (y(d.name) || 0) + y.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("text-anchor", "end")
      .style("font-size", "14px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .text(d => d.name);

    // Values
    g.selectAll(".value")
      .data(data)
      .enter()
      .append("text")
      .attr("class", "value")
      .attr("x", d => x(d.value) + 10)
      .attr("y", d => (y(d.name) || 0) + y.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("fill", "#ffffff")
      .text(d => `$${(d.value / 1000).toFixed(0)}B`);

    // X-axis
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(x).tickFormat(d => `$${(d as number / 1000).toFixed(0)}B`))
      .selectAll("text")
      .style("fill", "#ffffff");

    // Year display
    svg.append("text")
      .attr("x", width / 2 + margin.left)
      .attr("y", 40)
      .style("text-anchor", "middle")
      .style("font-size", "24px")
      .style("font-weight", "bold")
      .style("fill", "#6366f1")
      .text(currentYear);

  }, [currentYear]);

  const handlePlay = () => {
    if (isPlaying) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      setIsPlaying(false);
    } else {
      setIsPlaying(true);
      intervalRef.current = setInterval(() => {
        setCurrentYear(prev => {
          if (prev >= 2025) {
            setIsPlaying(false);
            return 1900;
          }
          return prev + 5;
        });
      }, 500);
    }
  };

  const handleYearChange = (year: number) => {
    setCurrentYear(year);
    if (isPlaying) {
      setIsPlaying(false);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <section className="py-20 bg-muted/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Global Brands{' '}
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Evolution
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Watch the rise and fall of the world's most valuable brands from 1900 to 2025
          </p>
        </div>

        {/* Chart Container */}
        <div className="bg-background/50 backdrop-blur-sm rounded-2xl border border-border p-8">
          <div className="flex flex-col items-center">
            {/* SVG Chart */}
            <svg
              ref={svgRef}
              width="800"
              height="500"
              className="mb-8"
            />

            {/* Controls */}
            <div className="flex flex-col items-center space-y-4 w-full max-w-2xl">
              {/* Play/Pause Button */}
              <button
                onClick={handlePlay}
                className="px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-primary/25 transition-all duration-300 transform hover:scale-105"
              >
                {isPlaying ? 'Pause' : 'Play'} Animation
              </button>

              {/* Year Slider */}
              <div className="w-full">
                <input
                  type="range"
                  min="1900"
                  max="2025"
                  step="5"
                  value={currentYear}
                  onChange={(e) => handleYearChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-2">
                  <span>1900</span>
                  <span className="font-bold text-primary">{currentYear}</span>
                  <span>2025</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #6366f1;
          cursor: pointer;
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #6366f1;
          cursor: pointer;
          border: none;
          box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
        }
      `}</style>
    </section>
  );
};

export default D3BarChart;
