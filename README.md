# <PERSON>'s Personal CMS Portfolio

A modern, galaxy-themed portfolio website built with Next.js, featuring 3D models, interactive animations, and a comprehensive content management system.

## 🌟 Features

- **Galaxy-themed Design**: Dark background with blue/purple accents and neon glow effects
- **Interactive 3D Models**: Three.js integration with rotating 3D models
- **Particle Background**: Animated starfield using tsparticles
- **D3.js Data Visualization**: Interactive bar chart race showing global brands evolution
- **Responsive Design**: Fully mobile-friendly using Tailwind CSS
- **Modern Typography**: Poppins for headings, Inter for body text
- **Smooth Animations**: Hover effects and scroll-triggered animations
- **Contact Form**: Functional contact form with validation
- **Blog Section**: Latest posts with newsletter signup
- **Portfolio Showcase**: Filterable project gallery

## 🚀 Tech Stack

- **Framework**: Next.js 15.3.3 with App Router
- **Styling**: Tailwind CSS 4.0
- **3D Graphics**: Three.js with React Three Fiber
- **Data Visualization**: D3.js
- **Particles**: tsparticles
- **Typography**: Google Fonts (Poppins, Inter)
- **Language**: TypeScript

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and theme
│   ├── layout.tsx           # Root layout with fonts
│   └── page.tsx             # Main page component
└── components/
    ├── Navbar.tsx           # Sticky navigation
    ├── Hero.tsx             # Hero section with 3D model
    ├── WhatWeOffer.tsx      # Services overview
    ├── Services.tsx         # Detailed services
    ├── Portfolio.tsx        # Project showcase
    ├── D3BarChart.tsx       # Interactive data visualization
    ├── Blog.tsx             # Blog posts section
    ├── CTA.tsx              # Call to action
    ├── Contact.tsx          # Contact form
    ├── Footer.tsx           # Site footer
    └── ParticleBackground.tsx # Animated background
```

## 🎨 Design System

### Colors

- **Background**: `#0a0a1a` (Dark galaxy)
- **Primary**: `#6366f1` (Indigo)
- **Secondary**: `#8b5cf6` (Purple)
- **Accent**: `#06b6d4` (Cyan)

### Typography

- **Headings**: Poppins (Bold, Modern)
- **Body**: Inter (Clean, Readable)

## 🛠️ Getting Started

1. **Install dependencies**:

```bash
npm install
```

2. **Run the development server**:

```bash
npm run dev
```

3. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📦 Dependencies

### Core

- `next`: 15.3.3
- `react`: ^19.0.0
- `typescript`: ^5

### 3D & Graphics

- `@react-three/fiber`: ^9.1.2
- `@react-three/drei`: ^10.1.2
- `three`: ^0.177.0

### Data Visualization

- `d3`: ^7.9.0

### Particles

- `@tsparticles/react`: ^3.0.0
- `@tsparticles/engine`: ^3.8.1
- `@tsparticles/basic`: Latest

### Styling

- `tailwindcss`: ^4

## 🎯 Sections Overview

1. **Hero**: Introduction with 3D model and CTA buttons
2. **What We Offer**: Brief overview of services
3. **Services**: Detailed service cards with features
4. **Portfolio**: Filterable project gallery
5. **D3 Chart**: Interactive brand evolution visualization
6. **Blog**: Latest posts with newsletter signup
7. **CTA**: Call to action with contact information
8. **Contact**: Contact form with social links
9. **Footer**: Site links and additional information

## 🔧 Customization

### Adding New Projects

Edit the `projects` array in `src/components/Portfolio.tsx`

### Modifying Colors

Update the CSS variables in `src/app/globals.css`

### Changing 3D Model

Replace the GLB file in `public/3d/` and update the path in `Hero.tsx`

## 📱 Responsive Design

The site is fully responsive with breakpoints:

- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

## 🚀 Deployment

The site is ready for deployment on Vercel, Netlify, or any other platform that supports Next.js.

## 📄 License

This project is for portfolio purposes. Feel free to use as inspiration for your own projects.

---

Built with ❤️ by Aziz Khan
