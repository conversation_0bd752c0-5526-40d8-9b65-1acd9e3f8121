Product Requirements Document (PRD) for <PERSON>'s Personal CMS

1. Overview
   Objective

Develop a modern, visually appealing CMS (Content Management System) for <PERSON>’s personal portfolio website, showcasing his expertise in 3D modeling and web development with an interactive, galaxy-themed UI.
Target Audience

    Potential clients

    Employers

    Fellow developers & designers

    Blog readers

Key Features

    Responsive, animated galaxy-themed design

    Interactive 3D model integration

    Dynamic D3.js bar chart race

    Blog management system

    Contact form with backend integration

2. Page Structure & Sections
1. Modern Navbar

   Sticky (fixed on scroll)

   Minimalist design with smooth hover effects

   Links to: Home, Services, Portfolio, Blog, Contact

   Dark theme with subtle glow effects

1. Hero Section (Header)

Left Side:

    Heading: "Hi, I'm <PERSON>"

    Subheading: "3D Modeler & Web Developer"

    Description: "Bringing your digital ideas to life."

    CTA Button: "View My Work" (links to Portfolio)

Right Side:

    Interactive 3D Model (rendered from public/3d/tb.glb)

    Smooth rotation animation on hover

Background:

    Animated Particle.js (twinkling stars, galaxy effect)

    Subtle parallax scrolling

3.  What We Offer (Brief Intro Section)

    Tagline: "Transforming Ideas into Digital Reality"

    Bullet Points:

        Custom 3D Modeling

        Web Development (React, Next.js, Three.js)

        Interactive UI/UX Design

4.  Services Section

    Card-based layout (3-4 services)

    Icons + Titles + Descriptions

        3D Modeling & Animation

        Web Development

        Interactive Design

        Consulting

5.  Portfolio Projects Section

    Grid layout with hover effects

    Filterable by category (3D, Web, Interactive)

    Each project:

        Thumbnail

        Title

        Short description

        Link to case study

6.  D3 Bar Chart Race (Top Global Brands 1800-2025)

    Animated bar chart race (D3.js)

    Time slider (1800 → 2025)

    Brand logos + values

    Pause/Play controls

7.  Blog Section

    Latest 3 posts (preview cards)

    Each card:

        Featured image

        Title

        Excerpt

        Read More button

    Link to full blog page

8.  Call to Action (CTA) Section

    Heading: "Ready to Bring Your Ideas to Life?"

    Subtext: "Let’s collaborate on your next project."

    Primary CTA Button: "Get in Touch" (links to Contact)

    Secondary CTA Button: "See My Work" (links to Portfolio)

9.  Contact Us Form

    Fields:

        Name

        Email

        Subject

        Message

    Submit Button (with loading state)

    Success/Error messages

10. Footer

    Quick Links (Home, Services, Portfolio, Blog, Contact)

    Social Media Icons (GitHub, LinkedIn, ArtStation, etc.)

    Copyright text

11. Design & Styling Guidelines
    Theme:

        Galaxy/Space Aesthetic

        Dark background (#0a0a1a) with blue/purple accents

        Neon glow effects on interactive elements

Typography:

    Headings: "Poppins" (Bold, Modern)

    Body Text: "Inter" (Clean, Readable)

Animations:

    Smooth hover effects (buttons, cards)

    Scroll-triggered animations (AOS.js)

    Particle.js for starry background

Responsiveness:

    Fully mobile-friendly (Tailwind CSS / Flexbox/Grid)

    Optimized for all screen sizes
